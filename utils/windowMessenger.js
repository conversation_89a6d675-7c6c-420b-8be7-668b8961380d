/**
 * 窗口消息通信类
 * 用于处理与播放器iframe的消息通信
 * 参考PC端实现，增强了跨域支持
 */

// 导入日志工具
// 如果没有logger，可以注释掉这一行并使用console
// import logger from './logger';

/**
 * 消息事件处理器类型
 */
// eslint-disable-next-line
const logger = console;

export class WindowMessenger {
  /**
   * 创建一个窗口消息通信实例
   * @param {Object} options - 配置选项
   * @param {string} [options.targetOrigin='*'] - 目标源
   * @param {Function} [options.onMessage] - 默认消息处理器
   * @param {Window} [options.window] - 目标窗口
   */
  constructor(options = {}) {
    // 目标源，默认为'*'，表示接受所有源
    this.targetOrigin = options.targetOrigin || '*';

    // 消息处理器映射
    this.messageHandlers = new Map();

    // 默认消息处理器
    this.defaultHandler = options.onMessage;

    // 使用真正的私有属性存储窗口引用，完全避免Vue响应式包装
    Object.defineProperty(this, '_targetWindow', {
      value: options.window || window,
      writable: true,
      enumerable: false,
      configurable: false
    });

    // 绑定消息监听器
    this.boundMessageHandler = this.handleMessage.bind(this);
    window.addEventListener('message', this.boundMessageHandler);

    logger.log('初始化WindowMessenger，目标源:', this.targetOrigin);
  }

  /**
   * 处理接收到的消息
   * @private
   * @param {MessageEvent} event - 消息事件
   */
  handleMessage(event) {
    try {
      // 验证消息来源 - 使用更宽松的验证
      if (this.targetOrigin !== '*' && event.origin !== this.targetOrigin) {
        logger.log('消息来源不匹配，已忽略。收到来自 ' + event.origin + ' 的消息，但期望来自 ' + this.targetOrigin);
        // 在开发环境下仍然处理消息，以便于调试
        // return;
      }

      // 处理消息数据
      let type, data;
      if (event.data && typeof event.data === 'object') {
        // 标准格式的消息
        type = event.data.type;
        data = event.data.data;
      } else {
        // 尝试解析其他格式的消息
        try {
          const parsed = JSON.parse(event.data);
          type = parsed.type;
          data = parsed.data;
        } catch (e) {
          logger.warn('无法解析消息数据:', event.data);
          type = 'unknown';
          data = event.data;
        }
      }

      logger.log('收到消息:', type, data);

      // 查找对应的处理器
      if (this.messageHandlers.has(type)) {
        this.messageHandlers.get(type)(data, event);
      } else if (this.defaultHandler) {
        this.defaultHandler(event.data, event);
      }
    } catch (error) {
      logger.error('处理消息时出错:', error);
    }
  }

  /**
   * 注册初始化处理器
   * @param {Function} handler - 处理函数
   */
  onInit(handler) {
    this.messageHandlers.set('onInit', handler);
  }

  /**
   * 注册视频结束事件处理器
   * @param {Function} handler - 处理函数
   */
  onVideoEnded(handler) {
    this.messageHandlers.set('onVideoEnded', handler);
  }

  /**
   * 发送初始化消息
   * @param {any} data - 消息数据
   */
  sendInit(data) {
    this.send('onInit', data);
  }

  /**
   * 设置目标窗口
   * @param {Window} window - 目标窗口
   */
  setWindow(window) {
    // 使用Object.defineProperty重新定义私有属性，完全避免Vue响应式包装
    try {
      // 检查window对象的有效性
      const targetWindow = window && typeof window.postMessage === 'function' ? window : null;

      // 重新定义私有属性
      Object.defineProperty(this, '_targetWindow', {
        value: targetWindow,
        writable: true,
        enumerable: false,
        configurable: true
      });

      logger.log('设置目标窗口:', !!targetWindow);
    } catch (error) {
      logger.error('设置目标窗口时出错:', error);

      // 如果出错，设置为null
      Object.defineProperty(this, '_targetWindow', {
        value: null,
        writable: true,
        enumerable: false,
        configurable: true
      });
    }
  }

  /**
   * 发送消息到目标窗口
   * @private
   * @param {string} type - 消息类型
   * @param {any} data - 消息数据
   */
  send(type, data) {
    try {
      // 使用私有属性，并加强防护
      const targetWindow = this._targetWindow;

      // 检查目标窗口是否有效
      if (!targetWindow) {
        logger.warn('目标窗口为空，无法发送消息');
        return;
      }

      if (targetWindow === window) {
        logger.warn('目标窗口与当前窗口相同，跳过发送');
        return;
      }

      // 检查是否有postMessage方法
      if (typeof targetWindow.postMessage !== 'function') {
        logger.error('目标窗口没有postMessage方法');
        return;
      }

      // 使用更安全的方式发送消息，确保数据是纯对象
      const message = {
        type: String(type),
        data: JSON.parse(JSON.stringify(data)) // 深度克隆，去除Vue响应式属性
      };

      logger.log('发送消息到目标窗口:', message, '目标源:', this.targetOrigin);
      targetWindow.postMessage(message, this.targetOrigin);

    } catch (error) {
      logger.error('发送消息时出错:', error);

      // 详细的错误信息
      if (error.name === 'SecurityError') {
        logger.error('跨域安全错误，可能是响应式对象问题');
      }
    }
  }

  /**
   * 销毁实例，清理事件监听
   */
  destroy() {
    logger.log('销毁WindowMessenger实例');
    window.removeEventListener('message', this.boundMessageHandler);
    this.messageHandlers.clear();
  }
}
