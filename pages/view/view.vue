<template>
  <view class="content-area">
    <MenuList />
  </view>

  <view class="container view">
    <!-- 加载中提示 -->
    <view class="loading-container" v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载视频...</text>
    </view>

    <!-- 错误提示 -->
    <view class="error-container" v-else-if="isError">
      <view class="error-icon">⚠️</view>
      <text class="error-text">视频加载失败，请刷新重试</text>
      <button class="error-button" @click="loadVideoDetail(videoId)">重试</button>
    </view>

    <!-- 视频内容 -->
    <view class="player-container" v-else>
      <view class="player">
        <view class="video">
          <!-- 使用iframe作为播放器，参考PC端实现 -->
          <iframe
            ref="iframePlayerRef"
            src="about:blank"
            frameborder="0"
            style="width: 100%; height: 100%;"
            border="0"
            marginwidth="0"
            marginheight="0"
            scrolling="no"
            allowfullscreen
            mozallowfullscreen="mozallowfullscreen"
            msallowfullscreen="msallowfullscreen"
            oallowfullscreen="oallowfullscreen"
            webkitallowfullscreen="webkitallowfullscreen"
            @error="handlePlayerError"
            @click="handleIframeClick"
          ></iframe>
        </view>
      </view>
    </view>

    <view class="content-area">
      <view class="videoinfo">
        <view class="biaoti1">
          {{ videoDetail?.videoName || '加载中...' }}
        </view>
        <div class="tagsrenqi">
          <span v-for="(tag, index) in videoTags" :key="index">
            <text class="tag-text">{{ tag }}</text>
          </span>
          <span><i class="icon renqi2"></i>{{ formatHits(videoDetail?.videoHits) }}</span>
        </div>

        <view v-if="playerBottomAd" class="viewpic2">
          <image :src="playerBottomAd.imageUrl" mode="widthFix" @click="handleAdClick(playerBottomAd)" />
        </view>

        <view class="bottom">
          <input type="text" :value="currentUrl" readonly />
          <button @click="copyUrl">点击复制转发</button>
        </view>

        <!-- 播放列表 -->
        <view class="playlist" v-if="playerItemList.length > 1">
          <ul>
            <li
                v-for="(item, index) in playerItemList"
                :key="index"
                :class="[currentPlayerItem === item ? 'on' : '']"
                @click="handleChangePlayerUrl(item)">
              {{ item.label }}</li>
          </ul>
        </view>
      </view>
      <view class="box">
        <view class="title">相关内容</view>

        <!-- 竖屏视频列表 -->
        <VerticalVideoList v-if="!isCurrentVideoHorizontal" :videoList="likeVideoList" />

        <!-- 横屏视频列表 -->
        <HorizontalVideoList v-else :videoList="likeVideoList" />


      </view>


      <!-- 更多推荐 -->
      <MoreRecommendations :recommendationList="comingSoonVideoList" />
    </view>
  </view>
</template>

<script>
import { getVideoDetail, getRandomVideos } from '@/api/video';
import { parseVodPlayUrl } from '@/utils/vodPlayer';
import { getAdvertisements, filterAdvertisementsByPosition, getAdvertisementsByPositions, getFirstAdvertisementByPosition, AdvertisementPosition } from '@/api/advertisement';
import cacheManager from '@/utils/cacheManager';
import config from '@/utils/config';
import { getCachedNavigationList } from '@/api/navigationCache';
import logger from '@/utils/logger';
import { WindowMessenger } from '@/utils/windowMessenger';
import MenuList from "@/components/common/menu.vue";
import VerticalVideoList from "@/components/common/VerticalVideoList.vue";
import HorizontalVideoList from "@/components/common/HorizontalVideoList.vue";
import MoreRecommendations from "@/components/common/MoreRecommendations.vue";

export default {
  name: 'VideoView',
  components: {
    MenuList,
    VerticalVideoList,
    HorizontalVideoList,
    MoreRecommendations
  },
  data() {
    return {
      videoId: '',
      videoDetail: null,
      videoTags: [],
      playerUrl: '', // 播放器URL
      currentPlayerItem: null,
      playerItemList: [],
      likeVideoList: [],
      comingSoonVideoList: [],
      isLoading: true,
      isError: false,
      // 广告数据
      allAdvertisements: [], // 全局广告数据
      playerTopRightAd: null, // 播放器右上广告
      playerBottomAd: null, // 播放器底部广告
      // 播放器内广告
      beforePlayAds: [], // 播放前广告
      duringPlayAds: [], // 播放中广告
      pausePlayAds: [], // 暂停时广告
      navigations: [], // 导航数据
      messenger: null, // 消息通信工具
      currentUrl: '', // 新增：存储当前页面 URL
      isCurrentVideoHorizontal: false // 当前视频是否为横屏视频

    }
  },
  watch: {
    playerBottomAd: {
      handler(newVal, oldVal) {
        logger.log('[广告监控] 底部广告数据变化:', {
          from: oldVal,
          to: newVal
        });
      },
      immediate: true
    }
  },
  async onLoad(options) {
    // 清空播放器
    if (this.$refs.iframePlayerRef) {
      this.$refs.iframePlayerRef.src = 'about:blank';
    }

    // 先加载导航数据
    await this.loadNavigations();

    this.currentUrl = window.location.href;

    if (options && options.id) {
      this.videoId = options.id;

      // 并行加载视频详情和广告
      await Promise.all([
        this.loadVideoDetail(this.videoId),
        this.loadAdvertisements()
      ]);
    } else {
      this.isError = true;
      uni.showToast({
        title: '视频ID不存在',
        icon: 'none'
      });
    }
  },

  onReady() {
    // 页面就绪后获取播放器引用
    this.$nextTick(() => {
      this.iframePlayerRef = this.$refs.iframePlayerRef;
      logger.info('播放器就绪:', !!this.iframePlayerRef);

      // 如果没有找到iframe元素，记录警告并设置重试
      if (!this.iframePlayerRef) {
        logger.warn('onReady中未找到iframe元素，将在组件渲染后重试');

        // 延迟一段时间后再次尝试获取iframe元素
        setTimeout(() => {
          this.iframePlayerRef = this.$refs.iframePlayerRef || document.querySelector('iframe');
          logger.info('延迟后重新检查播放器:', !!this.iframePlayerRef);
        }, 500);
      }

      // 初始化消息通信
      this.initMessenger();

      // 注意：我们已经在loadVideoDetail中使用setTimeout调用了handleChangePlayerUrl
      // 这里不需要重复调用，避免多次设置播放地址

      // 设置定时器，3秒后自动隐藏标题
      this.titleHideTimer = setTimeout(() => {
        this.isTitleVisible = false;
        this.titleHideTimer = null;
      }, 5000);
    });
  },

  onUnload() {
    // 页面卸载前清理播放器
    if (this.iframePlayerRef) {
      this.iframePlayerRef.src = 'about:blank';
    }

    // 销毁消息通信实例
    if (this.messenger) {
      this.messenger.destroy();
      this.messenger = null;
    }



  },
  methods: {
    // 加载视频详情，完全参考PC端实现
    async loadVideoDetail(id) {
      try {
        this.isLoading = true;

        // 获取视频详情
        this.videoDetail = await getVideoDetail(id);

        // 处理视频标签
        if (this.videoDetail && this.videoDetail.videoTag) {
          this.videoTags = this.videoDetail.videoTag.split(',');
        } else {
          this.videoTags = [];
        }

        // 解析播放地址
        this.playerItemList = parseVodPlayUrl(this.videoDetail?.videoPlayUrl || '')[0];
        logger.log('解析播放地址完成');

        // 设置默认播放项
        this.currentPlayerItem = this.playerItemList[0];

        // 延迟设置播放地址，确保页面已经渲染完成
        // 增加延迟时间，确保iframe元素已经渲染
        setTimeout(() => {
          logger.log('延迟设置播放地址');

          // 确保消息通信已初始化
          if (this.messenger) {
            // 设置目标窗口
            this.messenger.setWindow(this.iframePlayerRef?.contentWindow);
            logger.info('设置消息通信目标窗口');
          }

          this.handleChangePlayerUrl(this.currentPlayerItem);
        }, 1000); // 增加到1000ms

        // 判断当前视频是否为横屏视频（确保导航数据已加载）
        await this.ensureNavigationDataAndCheckVideoType();

        // 加载相关视频
        await this.loadLikeVideos();
        await this.loadComingSoonVideos();
      } catch (error) {
        logger.error('加载视频详情失败:', error);

        // 跳转到404页面
        uni.redirectTo({
          url: '/pages/error/404'
        });
      } finally {
        this.isLoading = false;
      }
    },

    // 加载视频页面广告（参考PC端实现）
    async loadAdvertisements() {
      try {
        logger.log('[视频页广告] 开始加载视频页广告');
        console.log('[DEBUG] 开始加载广告数据');

        // 为了调试，先清除缓存
        cacheManager.remove('advertisements');
        console.log('[DEBUG] 已清除广告缓存');

        // 尝试从缓存获取
        const cachedAds = cacheManager.get('advertisements');
        let advertisements;

        if (cachedAds) {
          logger.log('[视频页广告] 使用缓存数据');
          console.log('[DEBUG] 使用缓存的广告数据:', cachedAds);
          advertisements = cachedAds;
        } else {
          logger.log('[视频页广告] 从服务器获取广告数据');
          console.log('[DEBUG] 准备从服务器获取广告数据');
          // 从服务器获取
          advertisements = await getAdvertisements();
          console.log('[DEBUG] 从服务器获取到的广告数据:', advertisements);
          logger.log(`[视频页广告] 获取到${advertisements.length}个广告`);

          // 缓存数据，30分钟过期
          cacheManager.set('advertisements', advertisements, { expireTime: 30 * 60 * 1000 });
        }

        // 保存全局广告数据，供工具函数使用
        this.allAdvertisements = advertisements;
        console.log('[DEBUG] 保存的全局广告数据:', this.allAdvertisements);

        // 按照PC端方式获取广告
        this.playerBottomAd = getFirstAdvertisementByPosition(advertisements, AdvertisementPosition.PlayerBottom);
        console.log('[DEBUG] 播放器底部广告:', this.playerBottomAd);
        console.log('[DEBUG] AdvertisementPosition.PlayerBottom:', AdvertisementPosition.PlayerBottom);

        // 获取播放器内广告（用于传递给iframe）
        this.beforePlayAds = filterAdvertisementsByPosition(advertisements, AdvertisementPosition.BeforePlay);
        this.duringPlayAds = filterAdvertisementsByPosition(advertisements, AdvertisementPosition.DuringPlay);
        this.pausePlayAds = filterAdvertisementsByPosition(advertisements, AdvertisementPosition.PausePlay);

        logger.log(`[视频页广告] 设置右上广告: ${this.playerTopRightAd ? '有' : '无'}`);
        logger.log(`[视频页广告] 设置底部广告: ${this.playerBottomAd ? '有' : '无'}`);
        logger.log(`[视频页广告] 播放器内广告: 播放前${this.beforePlayAds.length}个, 播放中${this.duringPlayAds.length}个, 暂停${this.pausePlayAds.length}个`);

        // 强制触发视图更新
        this.$forceUpdate();

        // 调试信息：检查广告加载状态
        logger.log('[视频页广告] 广告加载完成，右上广告:', this.playerTopRightAd);
        logger.log('[视频页广告] 广告加载完成，底部广告:', this.playerBottomAd);

        return advertisements;
      } catch (error) {
        logger.error('[视频页广告] 加载广告失败:', error);

        // 失败时清空广告数据
        this.allAdvertisements = [];
        this.playerTopRightAd = null;
        this.playerBottomAd = null;
        this.beforePlayAds = [];
        this.duringPlayAds = [];
        this.pausePlayAds = [];

        // 显示错误提示（但不影响视频播放）
        uni.showToast({
          title: '广告加载失败',
          icon: 'none',
          duration: 2000
        });

        return [];
      }
    },

    /**
     * 设置播放器URL
     * 完全参考PC端实现
     * @param {string} url - 播放地址
     */
    setPlayerUrl(url) {
      // 检查URL有效性
      if (!url || url === 'about:blank') {
        logger.warn('无效的播放地址:', url);
        return;
      }

      logger.log('设置播放地址:', url);

      try {
        // 在移动端中，我们使用iframe作为播放器
        // 直接设置播放器URL
        this.playerUrl = url;

        // 输出调试信息
        logger.log('最终播放器URL:', this.playerUrl);

        // 显示加载提示
        uni.showToast({
          title: '视频加载中',
          icon: 'loading',
          duration: 2000
        });
      } catch (error) {
        logger.error('设置播放地址失败:', error);
        this.handlePlayerError();
      }
    },

    /**
     * 切换播放地址
     * 完全参考PC端实现
     * @param {Object} item - 播放项
     */
    handleChangePlayerUrl(item) {
      if (!item) {
        logger.warn('播放项为空');
        return;
      }

      // 更新页面标题，包含视频名称和当前播放源
      const titleName = this.videoDetail?.videoName + ' - ' + item.label;
      uni.setNavigationBarTitle({
        title: titleName
      });

      // 更新当前播放项
      this.currentPlayerItem = item;

      // 输出调试信息
      logger.info('切换播放项:', item.label);
      logger.info('原始播放地址:', item.url);
      logger.info('videoPlayServer:', this.videoDetail?.videoPlayServer);

      // 获取完整的播放地址
      const playUrl = this.videoDetail?.videoPlayServer + item.url;
      logger.info('最终播放地址:', playUrl);

        // 直接使用原始播放地址

      try {
        // 优先使用已保存的引用
        let iframe = this.iframePlayerRef;

        // 如果没有保存的引用，尝试使用$refs
        if (!iframe) {
          iframe = this.$refs.iframePlayerRef;
          if (iframe) {
            // 保存引用以便后续使用
            this.iframePlayerRef = iframe;
            logger.info('使用$refs找到iframe元素');
          }
        }

        // 如果仍然没有找到，尝试使用document.querySelector
        if (!iframe) {
          iframe = document.querySelector('iframe');
          if (iframe) {
            // 保存引用以便后续使用
            this.iframePlayerRef = iframe;
            logger.info('使用querySelector找到iframe元素');
          }
        }

        if (iframe) {
          // 直接设置播放地址
          logger.info('设置播放地址:', playUrl);
          iframe.src = playUrl;

          // 设置消息通信目标窗口
          if (this.messenger) {
            // 使用iframe的contentWindow而不是当前window
            this.messenger.setWindow(iframe.contentWindow);
            logger.info('设置消息通信目标窗口');
          }

          // 添加加载事件监听
          iframe.onload = () => {
            logger.info('iframe加载成功');
            this.isLoading = false;
          };

          iframe.onerror = () => {
            logger.error('iframe加载失败');
            this.handlePlayerError();
          };
        } else {
          logger.error('无法找到iframe元素，将在短暂延迟后重试');

          // 如果仍然找不到iframe，延迟一段时间后重试一次
          setTimeout(() => {
            const retryIframe = this.$refs.iframePlayerRef || document.querySelector('iframe');
            if (retryIframe) {
              logger.info('延迟后找到iframe元素');
              // 保存引用
              this.iframePlayerRef = retryIframe;
              // 设置播放地址
              retryIframe.src = playUrl;

              // 设置消息通信目标窗口
              if (this.messenger) {
                this.messenger.setWindow(retryIframe.contentWindow);
                logger.info('延迟后设置消息通信目标窗口');
              }

              // 添加加载事件监听
              retryIframe.onload = () => {
                logger.info('iframe延迟加载成功');
                this.isLoading = false;
              };
            } else {
              logger.error('延迟后仍无法找到iframe元素');
            }
          }, 500); // 延迟500ms后重试
        }
      } catch (error) {
        logger.error('设置播放地址失败:', error);
      }

      // 记录播放历史
      this.savePlayHistory();
    },

    /**
     * 记录播放历史
     */
    savePlayHistory() {
      if (!this.videoDetail || !this.currentPlayerItem) return;

      try {
        // 构建历史记录对象
        const historyItem = {
          id: this.videoDetail.id,
          videoName: this.videoDetail.videoName,
          videoPic: this.videoDetail.videoPic,
          videoType: this.videoDetail.videoType,
          currentEpisode: this.currentPlayerItem.label,
          timestamp: Date.now()
        };

        // 获取当前栏目的extraParams信息
        // 使用已加载的导航数据，避免动态导入
        if (this.navigations && this.navigations.length > 0) {
          // 查找当前栏目
          const currentNavItem = this.navigations.find(nav =>
            nav.categotyName === this.videoDetail.videoType || nav.name === this.videoDetail.videoType
          );

          // 如果找到当前栏目，将extraParams添加到历史记录中
          if (currentNavItem && currentNavItem.extraParams) {
            historyItem.extraParams = currentNavItem.extraParams;
          }
        } else {
          // 如果没有导航数据，则不添加extraParams
          logger.info('没有导航数据，无法获取extraParams');
        }

        // 获取现有历史
        let history = uni.getStorageSync('playHistory') || [];
        if (typeof history === 'string') {
          try {
            history = JSON.parse(history);
          } catch (e) {
            history = [];
          }
        }

        // 移除相同视频的旧记录
        history = history.filter(item => item.id !== historyItem.id);

        // 添加新记录到开头
        history.unshift(historyItem);

        // 限制历史记录数量
        if (history.length > 50) {
          history = history.slice(0, 50);
        }

        // 保存历史
        uni.setStorageSync('playHistory', JSON.stringify(history));
      } catch (error) {
        logger.error('保存播放历史失败:', error);
      }
    },

    // 处理播放器错误
    handlePlayerError() {
      logger.error('播放器错误:', this.currentPlayerItem);

      // 显示错误提示
      uni.showToast({
        title: '视频播放失败，请尝试其他线路',
        icon: 'none'
      });

      // 如果有多个播放源，尝试切换到下一个
      if (this.playerItemList.length > 1) {
        const currentIndex = this.playerItemList.findIndex(item => item === this.currentPlayerItem);
        if (currentIndex >= 0 && currentIndex < this.playerItemList.length - 1) {
          this.handleChangePlayerUrl(this.playerItemList[currentIndex + 1]);
        }
      }
    },

    // 处理播放器点击事件，完全参考PC端实现
    handleClickPlayer(id) {
      // 处理播放器点击事件
      uni.redirectTo({
        url: `/pages/view/view?id=${id}`
      });
    },


    // 初始化消息通信（完全参考PC端实现）
    initMessenger() {
      // 创建消息通信实例
      this.messenger = new WindowMessenger({
        targetOrigin: '*',
        onMessage: (data, event) => {
          // 处理所有消息，以防有不同格式的消息
          logger.info('收到消息:', data);

          // 检查是否是视频结束消息
          if (data && (
              (data.type === 'onVideoEnded') ||
              (data.type === 'videoEnded') ||
              (typeof data === 'string' && data.includes('videoEnded'))
          )) {
            logger.info('检测到视频结束消息');
            this.handleVideoEnded();
          }
        }
      });

      // 关键：绑定到当前窗口监听消息（参考PC端实现）
      this.messenger.Bind(window);
      logger.info('已绑定消息监听到当前窗口');

      // 监听视频结束事件
      this.messenger.onVideoEnded((data) => {
        logger.info('视频播放结束，准备联播:', data);
        this.handleVideoEnded();
      });

      // 监听初始化事件，在播放器准备好时传递广告数据（参考PC端实现）
      this.messenger.onInit(() => {
        logger.info('播放器初始化完成，传递广告数据');
        if (this.videoDetail) {
          // 按照PC端方式传递广告数据
          this.messenger.sendInit({
            videoPic: this.videoDetail.videoPic,
            ads: JSON.parse(JSON.stringify(getAdvertisementsByPositions(this.allAdvertisements, [
              AdvertisementPosition.DuringPlay,
              AdvertisementPosition.PausePlay,
              AdvertisementPosition.BeforePlay
            ])))
          });
          logger.log('[播放器广告] 广告数据已通过onInit传递');
        }
      });

      logger.info('消息通信初始化完成');
    },

    // 向播放器传递广告数据（参考PC端实现）
    sendAdsToPlayer() {
      if (!this.messenger) {
        logger.warn('[播放器广告] 消息通信未初始化');
        return;
      }

      if (!this.videoDetail) {
        logger.warn('[播放器广告] 视频详情未加载');
        return;
      }

      try {
        // 按照PC端方式准备广告数据，确保是纯对象
        const playerAds = getAdvertisementsByPositions(this.allAdvertisements, [
          AdvertisementPosition.DuringPlay,
          AdvertisementPosition.PausePlay,
          AdvertisementPosition.BeforePlay
        ]);

        // 深度克隆，去除Vue响应式属性
        const adsData = {
          videoPic: this.videoDetail?.videoPic || '',
          ads: JSON.parse(JSON.stringify(playerAds))
        };

        logger.log('[播放器广告] 准备传递广告数据:', adsData);
        logger.log(`[播放器广告] 广告数量: 播放前${this.beforePlayAds.length}个, 播放中${this.duringPlayAds.length}个, 暂停${this.pausePlayAds.length}个`);
        logger.log('[播放器广告] 广告详情:', adsData.ads);

        // 先发送一个测试消息
        this.messenger.send('test', { message: '测试消息' });

        // 传递初始化数据到播放器
        this.messenger.sendInit(adsData);

        // 再发送一个确认消息
        this.messenger.send('adsDataSent', {
          adCount: adsData.ads.length,
          videoPic: adsData.videoPic
        });

        logger.log('[播放器广告] 广告数据传递成功');
      } catch (error) {
        logger.error('[播放器广告] 传递广告数据失败:', error);
      }
    },

    // 处理视频结束事件
    handleVideoEnded() {

      // 检查是否有下一集
      const nextItem = this.getNextPlayerItem();

      if (nextItem) {
        // 有下一集，直接播放
        logger.info('开始播放下一集:', nextItem.label);
        this.handleChangePlayerUrl(nextItem);
      } else {
        // 没有下一集，播放推荐视频
        this.playFirstRecommendation();
      }
    },

    // 获取下一个播放项
    getNextPlayerItem() {
      if (!this.playerItemList || !this.currentPlayerItem) {
        logger.info('无法获取下一集: 播放列表或当前项为空');
        return null;
      }

      logger.info('当前播放项:', this.currentPlayerItem.label, this.currentPlayerItem.url);
      logger.info('播放列表长度:', this.playerItemList.length);

      const currentIndex = this.playerItemList.findIndex(
        item => item.url === this.currentPlayerItem.url
      );

      logger.info('当前播放项索引:', currentIndex);

      if (currentIndex === -1 || currentIndex === this.playerItemList.length - 1) {
        logger.info('没有下一集');
        return null;
      }

      logger.info('找到下一集:', this.playerItemList[currentIndex + 1].label);
      return this.playerItemList[currentIndex + 1];
    },

    // 播放第一个推荐视频
    playFirstRecommendation() {
      if (!this.likeVideoList || this.likeVideoList.length === 0) {
        logger.info('无推荐视频可播放');
        return;
      }

      // 确保有推荐视频
      const recommendVideo = this.likeVideoList[0];
      if (!recommendVideo || !recommendVideo.id) {
        logger.info('推荐视频数据无效');
        return;
      }

      logger.info('开始播放推荐视频:', recommendVideo.videoName || recommendVideo.title);

      // 没有需要清除的定时器

      // 直接跳转到推荐视频
      setTimeout(() => {
        uni.redirectTo({
          url: `/pages/view/view?id=${recommendVideo.id}`
        });
      }, 500); // 等待短暂再跳转，避免可能的冲突
    },

    // 处理列表导航
    handleNavigateToList(params) {
      if (!params) return;

      logger.log('导航到列表:', params);

      // 解析参数，提取标签名称
      let tagName = '';
      if (params.startsWith('videoTag=')) {
        tagName = decodeURIComponent(params.substring('videoTag='.length));
      }

      // 构建完整的URL，包含标签名称作为页面标题
      let url = `/pages/list/list?${params}`;

      // 如果有标签名称，添加到URL中
      if (tagName) {
        url += `&title=${encodeURIComponent(tagName)}`;
      }

      uni.navigateTo({
        url: url
      });
    },

    // 处理广告点击
    handleAdClick(ad) {
      if (!ad) {
        logger.warn('[视频页广告] 点击的广告数据为空');
        return;
      }

      logger.log('[视频页广告] 点击广告:', ad);

      if (ad.redirectUrl) {
        logger.log('[视频页广告] 跳转地址:', ad.redirectUrl);

        if (ad.redirectUrl.startsWith('http')) {
          // 外部链接，在新窗口中打开
          logger.log('[视频页广告] 在新窗口打开外部链接');
          window.open(ad.redirectUrl, '_blank');
        } else {
          // 内部页面
          logger.log('[视频页广告] 跳转到内部页面');
          uni.navigateTo({
            url: ad.redirectUrl
          });
        }
      } else {
        logger.warn('[视频页广告] 广告没有跳转地址');
      }
    },

    // 加载相关视频
    async loadLikeVideos() {
      try {
        // 设置需要获取的视频总数
        const totalVideosNeeded = 12;

        // 首先尝试获取相同 videoType 的视频
        let sameTypeVideos = [];

        if (this.videoDetail && this.videoDetail.videoType) {
          logger.info('尝试获取相同 videoType 的视频:', this.videoDetail.videoType);

          // 获取相同 videoType 的视频，排除当前视频
          const sameTypeResponse = await getRandomVideos({
            maxResultCount: totalVideosNeeded,
            skipCount: 0,
            videoType: this.videoDetail.videoType,
            sorting: 'Random'
          });

          if (sameTypeResponse && (Array.isArray(sameTypeResponse) || sameTypeResponse.items)) {
            const items = Array.isArray(sameTypeResponse) ? sameTypeResponse : sameTypeResponse.items;
            // 排除当前视频
            sameTypeVideos = items.filter(video => video.id !== this.videoDetail.id);
            logger.info('获取到相同 videoType 的视频数量:', sameTypeVideos.length);
          }
        }

        // 如果相同 videoType 的视频数量足够，直接使用
        if (sameTypeVideos.length >= totalVideosNeeded) {
          this.likeVideoList = this.shuffleArray(sameTypeVideos).slice(0, totalVideosNeeded);
          return;
        }

        // 如果相同 videoType 的视频数量不足，需要从当前栏目中获取更多视频来填充
        const remainingNeeded = totalVideosNeeded - sameTypeVideos.length;
        logger.info('需要从当前栏目中获取更多视频:', remainingNeeded);

        // 使用缓存服务获取导航数据
        const { getCachedNavigationList } = await import('@/api/navigationCache');
        const navigationResponse = await getCachedNavigationList();

        // 获取当前栏目信息
        const currentNavItem = navigationResponse.find(nav => {
          return nav.categotyName === this.videoDetail.videoType;
        });

        // 判断当前栏目是否为短视频栏目
        let isShortVideoCategory = false;
        if (currentNavItem && currentNavItem.extraParams) {
          try {
            const extraParamsObj = JSON.parse(currentNavItem.extraParams);
            isShortVideoCategory = extraParamsObj && extraParamsObj.isHorizontalLayout === true;
          } catch (error) {
            logger.error('解析extraParams失败:', error, currentNavItem.extraParams);
          }
        }

        // 如果当前栏目是短视频栏目，从其他短视频栏目中获取视频
        // 否则，使用标签获取相关视频
        if (isShortVideoCategory) {
          // 筛选所有短视频栏目（extraParams 中 isHorizontalLayout 为 true）
          const shortVideoCategories = navigationResponse
            .filter(nav => {
              if (!nav.isH5Display) return false;

              // 解析 extraParams
              let extraParamsObj = null;
              try {
                if (nav.extraParams && typeof nav.extraParams === 'string') {
                  extraParamsObj = JSON.parse(nav.extraParams);
                }
              } catch (error) {
                logger.error('解析extraParams失败:', error, nav.extraParams);
                return false;
              }

              // 筛选 isHorizontalLayout 为 true 的项目，但排除当前栏目
              return extraParamsObj && extraParamsObj.isHorizontalLayout === true &&
                     nav.categotyName !== this.videoDetail.videoType;
            })
            .map(nav => nav.categotyName);

          // 如果没有其他短视频栏目，使用标签获取相关视频
          if (!shortVideoCategories.length) {
            const tagResponse = await getRandomVideos({
              videoTag: this.videoDetail.videoTag,
              maxResultCount: remainingNeeded,
              skipCount: 0,
              sorting: 'Random'
            });

            const tagVideos = Array.isArray(tagResponse) ? tagResponse : (tagResponse ? tagResponse.items : []);

            // 合并相同 videoType 的视频和标签相关的视频
            this.likeVideoList = [...sameTypeVideos, ...tagVideos].slice(0, totalVideosNeeded);
            return;
          }

          // 随机打乱栏目顺序，增强随机性
          const shuffledCategories = this.shuffleArray(shortVideoCategories);

          // 计算每个栏目需要获取的视频数量
          let videosPerCategory;
          let categoriesToUse;

          if (shuffledCategories.length <= remainingNeeded) {
            // 每个栏目至少获取一个视频，剩下的平均分配
            videosPerCategory = Math.ceil(remainingNeeded / shuffledCategories.length);
            categoriesToUse = shuffledCategories;
          } else {
            // 随机选择remainingNeeded个栏目，每个获取一个视频
            videosPerCategory = 1;
            categoriesToUse = shuffledCategories.slice(0, remainingNeeded);
          }

          // 并行请求所有选中栏目的视频
          const requests = categoriesToUse.map(category => {
            return getRandomVideos({
              maxResultCount: videosPerCategory,
              skipCount: 0,
              videoType: category,
              sorting: 'Random'
            });
          });

          // 等待所有请求完成
          const responses = await Promise.all(requests);

          // 处理所有响应
          const otherVideos = [];
          responses.forEach(response => {
            if (response && (Array.isArray(response) || response.items)) {
              const items = Array.isArray(response) ? response : response.items;
              otherVideos.push(...items);
            }
          });

          // 合并相同 videoType 的视频和其他栏目的视频
          const allVideos = [...sameTypeVideos, ...this.shuffleArray(otherVideos)];
          this.likeVideoList = allVideos.slice(0, totalVideosNeeded);
        } else {
          // 如果不是短视频栏目，使用标签获取相关视频
          const tagResponse = await getRandomVideos({
            videoTag: this.videoDetail.videoTag,
            maxResultCount: remainingNeeded,
            skipCount: 0,
            sorting: 'Random'
          });

          const tagVideos = Array.isArray(tagResponse) ? tagResponse : (tagResponse ? tagResponse.items : []);

          // 合并相同 videoType 的视频和标签相关的视频
          this.likeVideoList = [...sameTypeVideos, ...tagVideos].slice(0, totalVideosNeeded);
        }
      } catch (error) {
        logger.error('加载相关视频失败:', error);
        this.likeVideoList = [];
      }
    },

    // 加载即将播放视频
    async loadComingSoonVideos() {
      try {
        // 设置需要获取的视频总数
        const totalVideosNeeded = 5;

        // 使用缓存服务获取导航数据
        const { getCachedNavigationList } = await import('@/api/navigationCache');
        const navigationResponse = await getCachedNavigationList();

        // 筛选 extraParams 中 isHorizontalLayout 为 true 的栏目
        const shortVideoCategories = navigationResponse
          .filter(nav => {
            if (!nav.isH5Display) return false;

            // 解析 extraParams
            let extraParamsObj = null;
            try {
              if (nav.extraParams && typeof nav.extraParams === 'string') {
                extraParamsObj = JSON.parse(nav.extraParams);
              }
            } catch (error) {
              logger.error('解析extraParams失败:', error, nav.extraParams);
              return false;
            }

            // 筛选 isHorizontalLayout 为 true 的项目
            return extraParamsObj && extraParamsObj.isHorizontalLayout === true;
          })
          .map(nav => nav.categotyName);

        // 如果没有找到 extraParams 为 "0" 的栏目，使用默认方式获取视频
        if (!shortVideoCategories.length) {
          // 使用默认方式获取视频（基于视频类型）
          const response = await getRandomVideos({
            videoType: this.videoDetail.videoType,
            maxResultCount: totalVideosNeeded,
            skipCount: 0,
            sorting: 'Random'
          });

          this.comingSoonVideoList = response || [];
          return;
        }

        // 随机打乱栏目顺序，增强随机性
        const shuffledCategories = this.shuffleArray(shortVideoCategories);

        // 计算每个栏目需要获取的视频数量
        let videosPerCategory;
        let categoriesToUse;

        if (shuffledCategories.length <= totalVideosNeeded) {
          // 每个栏目至少获取一个视频，剩下的平均分配
          videosPerCategory = Math.ceil(totalVideosNeeded / shuffledCategories.length);
          categoriesToUse = shuffledCategories;
        } else {
          // 随机选择totalVideosNeeded个栏目，每个获取一个视频
          videosPerCategory = 1;
          categoriesToUse = shuffledCategories.slice(0, totalVideosNeeded);
        }

        // 获取所有选中栏目的视频
        const allVideos = [];

        // 并行请求所有选中栏目的视频
        const requests = categoriesToUse.map(category => {
          return getRandomVideos({
            maxResultCount: videosPerCategory,
            skipCount: 0,
            videoType: category,
            sorting: 'Random'
          });
        });

        // 等待所有请求完成
        const responses = await Promise.all(requests);

        // 处理所有响应
        responses.forEach(response => {
          if (response && (Array.isArray(response) || response.items)) {
            const items = Array.isArray(response) ? response : response.items;
            allVideos.push(...items);
          }
        });

        // 如果没有获取到视频，尝试使用默认方式
        if (allVideos.length === 0) {
          const response = await getRandomVideos({
            videoType: this.videoDetail.videoType,
            maxResultCount: totalVideosNeeded,
            skipCount: 0,
            sorting: 'Random'
          });

          this.comingSoonVideoList = response || [];
          return;
        }

        // 随机打乱并限制数量
        this.comingSoonVideoList = this.shuffleArray(allVideos).slice(0, totalVideosNeeded);
      } catch (error) {
        logger.error('加载即将播放视频失败:', error);
        this.comingSoonVideoList = [];
      }
    },

    // 格式化播放量
    formatHits(hits) {
      if (!hits) return '0';

      const num = parseInt(hits);
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万';
      }
      return num.toString();
    },

    // 格式化数字（用于横屏视频）
    formatNumber(num) {
      if (!num) return '0';

      const number = parseInt(num);
      if (number >= 10000) {
        return (number / 10000).toFixed(1) + '万';
      }
      return number.toString();
    },



    /**
     * 随机打乱数组
     * @param {Array} array - 要打乱的数组
     * @returns {Array} 打乱后的数组
     */
    shuffleArray(array) {
      if (!array || !Array.isArray(array)) return [];
      const newArray = [...array];
      for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
      }
      return newArray;
    },

    // 跳转到列表页
    navigateToList(params) {
      uni.navigateTo({
        url: `/pages/list/list?${params}`
      });
    },



    // 加载导航数据
    async loadNavigations() {
      try {
        const response = await getCachedNavigationList();
        logger.info('原始导航数据响应:', response);

        if (response && Array.isArray(response)) {
          this.navigations = response;
          logger.info('导航数据加载成功，数量:', this.navigations.length);

          // 输出前几个导航项的详细信息
          this.navigations.slice(0, 3).forEach((nav, index) => {
            logger.info(`导航项${index + 1}:`, {
              name: nav.name,
              categotyName: nav.categotyName,
              extraParams: nav.extraParams,
              isH5Display: nav.isH5Display
            });
          });
        }
      } catch (error) {
        logger.error('加载导航数据失败:', error);
        this.navigations = [];
      }
    },

    // 确保导航数据已加载并判断视频类型
    async ensureNavigationDataAndCheckVideoType() {
      // 如果导航数据为空，重新加载
      if (!this.navigations || this.navigations.length === 0) {
        logger.info('导航数据为空，重新加载...');
        await this.loadNavigations();
      }

      // 然后判断视频类型
      this.checkIfCurrentVideoIsHorizontal();
    },

    // 判断当前视频是否为横屏视频
    checkIfCurrentVideoIsHorizontal() {
      logger.info('开始判断视频类型...');
      logger.info('视频详情:', this.videoDetail);
      logger.info('导航数据数量:', this.navigations.length);

      // 输出所有导航项的categotyName和extraParams
      if (this.navigations.length > 0) {
        logger.info('所有导航项的categotyName:');
        this.navigations.forEach((nav, index) => {
          logger.info(`  ${index + 1}. categotyName: "${nav.categotyName}", extraParams: ${nav.extraParams}`);
        });
      }

      if (!this.videoDetail || !this.videoDetail.videoType || !this.navigations.length) {
        logger.warn('数据不完整，默认为竖屏视频');
        this.isCurrentVideoHorizontal = false;
        return;
      }

      // 解析videoType，可能包含多个分类（用逗号分隔）
      const videoTypes = this.videoDetail.videoType.split(',').map(type => type.trim());
      logger.info('解析后的视频类型数组:', videoTypes);

      // 查找当前视频类型对应的导航项
      // 尝试匹配videoType中的任一分类与导航数据中的categotyName
      let currentNavItem = null;
      for (const videoType of videoTypes) {
        currentNavItem = this.navigations.find(nav =>
          nav.categotyName === videoType
        );
        if (currentNavItem) {
          logger.info(`匹配成功 - 视频类型: "${videoType}" 匹配到导航项:`, currentNavItem);
          break;
        }
      }

      logger.info('当前视频完整类型:', this.videoDetail.videoType);
      logger.info('最终找到的导航项:', currentNavItem);

      if (currentNavItem && currentNavItem.extraParams) {
        try {
          logger.info('原始extraParams:', currentNavItem.extraParams);

          // 先尝试解析为JSON
          if (typeof currentNavItem.extraParams === 'string') {
            const extraParamsObj = JSON.parse(currentNavItem.extraParams);
            logger.info('解析后的extraParams:', extraParamsObj);
            this.isCurrentVideoHorizontal = extraParamsObj && extraParamsObj.isHorizontalLayout === true;
          } else {
            // 如果不是字符串，直接使用
            this.isCurrentVideoHorizontal = currentNavItem.extraParams && currentNavItem.extraParams.isHorizontalLayout === true;
          }

          logger.info('最终判断结果 - 当前视频类型:', this.videoDetail.videoType, '是否横屏:', this.isCurrentVideoHorizontal);
        } catch (error) {
          logger.error('解析extraParams失败:', error, currentNavItem.extraParams);
          // 兼容旧版本：检查是否为 '0'
          this.isCurrentVideoHorizontal = currentNavItem.extraParams === '0';
          logger.info('兼容性判断结果:', this.isCurrentVideoHorizontal);
        }
      } else {
        logger.warn('没有找到匹配的导航项');
        logger.info('尝试匹配的视频类型:', videoTypes);
        logger.info('可用的导航项categotyName:', this.navigations.map(nav => nav.categotyName));

        // 特殊情况：如果视频类型包含"短剧"，直接判断为横屏
        if (videoTypes.some(type => type.includes('短剧'))) {
          this.isCurrentVideoHorizontal = true;
          logger.info('基于视频类型包含"短剧"判断为横屏');
        } else {
          logger.info('没有找到对应的导航项或extraParams，默认为竖屏');
          this.isCurrentVideoHorizontal = false;
        }
      }
    },

    // 导航到视频所属的频道页面
    navigateToChannel() {
      if (!this.videoDetail || !this.videoDetail.videoType) {
        // 如果没有视频类型信息，返回上一页
        uni.navigateBack({ delta: 1 });
        return;
      }

      // 获取视频类型
      const videoType = this.videoDetail.videoType;

      // 记录日志
      logger.log('将导航到视频类型:', videoType);

      // 跳转到首页并传递视频类型参数
      uni.switchTab({
        url: '/pages/index/index',
        success: () => {
          // 使用全局事件通知首页切换到对应的标签
          setTimeout(() => {
            uni.$emit('switch-tab-by-category', videoType);
          }, 500); // 延迟发送事件，确保首页已加载完成
        }
      });
    },

    copyUrl() {
      try {
        uni.setClipboardData({
          data: this.currentUrl,
          success: () => {
            uni.showToast({
              title: '链接已复制',
              icon: 'success'
            });
          },
          fail: (err) => {
            logger.error('复制链接失败:', err);
            uni.showToast({
              title: '复制失败，请重试',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        logger.error('复制链接时出错:', error);
        uni.showToast({
          title: '复制失败，请重试',
          icon: 'none'
        });
      }
    }
  }
}
</script>


<style scoped lang="scss">

// 广告容器样式
.ads-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

// 加载状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 420rpx;
  background-color: #f5f5f5;

  .loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #e62c17;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 错误状态样式
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 420rpx;
  background-color: #f5f5f5;

  .error-icon {
    font-size: 60rpx;
    margin-bottom: 20rpx;
  }

  .error-text {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 30rpx;
  }

  .error-button {
    padding: 16rpx 40rpx;
    background-color: #e62c17;
    color: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    border: none;
  }
}

// 播放器容器
.player-container {
  position: relative;
}

// 右侧广告
.player-right-ad {
  position: absolute;
  top: 60rpx;
  right: 20rpx;
  width: 200rpx;
  z-index: 10;

  image {
    width: 100%;
    border-radius: 8rpx;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  }
}

.player {
  position: relative;
  z-index: 1; /* 确保player在最上层 */

  .biaoti {
    display: flex;
    position: absolute;
    z-index: 99;
    top: 16rpx;
    left: 10rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #fff;
    align-items: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: calc(100% - 20rpx);;

    span {
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    i.icon:before {
      height: 40rpx;
      width: 40rpx;
      margin: 0;
    }
  }

  .video {
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 比例 */
    position: relative;
    overflow: hidden;
    background: #000;

    iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
      z-index: 1;
    }

    .video-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2; /* 确保覆盖层在iframe上方 */
      background: transparent; /* 透明背景 */
    }



    .placeholder {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: #000;

      image {
        width: 120rpx;
        height: 120rpx;
        margin-bottom: 20rpx;
      }

      text {
        color: #fff;
        font-size: 28rpx;
      }
    }
  }

}

.videoinfo {
  .biaoti1 {
    font-size: 36rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: 600;
  }

  .tagsrenqi {
    display: flex;
    align-items: center;
    height: 60rpx;
    font-size: 24rpx;
    gap: 20rpx;
    color: #6f6f71;
    padding: 16rpx 0;

    span {
      display: flex;
      align-items: center;

      text {
        background-color: #2a2a2a;
        padding: 4rpx 12rpx;
        border-radius: 6rpx;
        color: #969699;
      }

      .tag-text {
        cursor: default; /* 确保标签文本不显示为可点击状态 */
      }
    }
  }

  .tagsrenqi {
    span:last-child {
      display: flex;
      align-items: center;
      color: #e62c17;

      text {
        background-color: transparent;
        padding: 0;
        color: #e62c17;
      }
    }

    .renqi2:before {
      width: 36rpx;
      height: 36rpx;
      margin-left: 0;
      margin-right: 6rpx;
    }
  }


  .h5pic image {
    width: 100%;
    display: block;
  }

  .playlist ul {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 30rpx;
  }

  .playlist li {
    min-width: calc(16.6667% - 30px);
    padding: 0 10px;
    box-sizing: initial;
    display: flex;
    background-color: #101227;
    color: #fff6;
    min-height: 80rpx;
    justify-content: center;
    align-items: center;
  }

  .playlist li.on {
    color: #fc748c;
    background: url('/static/play.gif') no-repeat left bottom;
    background-color: #101227;
  }



  //.bottom {
  //  display: flex;
  //  justify-content: space-around;
  //  background: #ffffff1a;
  //  color: #6f6f71;
  //  font-size: 26rpx;
  //
  //  span {
  //    display: flex;
  //    align-items: center;
  //  }
  //
  //  span:nth-child(1) i.icon:before, span:nth-child(2) i.icon:before {
  //    opacity: 0.4;
  //  }
  //
  //  span:nth-child(2) i.icon:before {
  //    transform: scaleY(-1);
  //  }
  //
  //  i.icon:before {
  //    width: 48rpx;
  //    height: 48rpx;
  //  }
  //}
}









/* 标题显示隐藏样式 */
.biaoti {
  transition: opacity 0.3s ease;
  opacity: 1;
}

.biaoti.hidden {
  opacity: 0;
}

.content-area .menu{
  margin-bottom: 0;
}


.videoinfo .bottom {
  display: flex;
  align-items: center;
  background: #ffffff1a;
  padding: 20rpx;
  gap: 20rpx;

  input {
    flex: 1;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8rpx;
    padding: 10rpx;
    font-size: 26rpx;
    color: #333;
  }

  button {
    background-color: #fc748c;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    padding: 0 20rpx;
    font-size: 26rpx;
  }
}


.viewpic2 image{
  width: 100%;
}
</style>
